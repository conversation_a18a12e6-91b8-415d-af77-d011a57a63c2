#!/bin/bash

echo "OPC UA C++ 客户端编译脚本"
echo "========================"

# 检查是否存在build目录
if [ ! -d "build" ]; then
    echo "创建build目录..."
    mkdir build
fi

cd build

echo "配置CMake项目..."
cmake ..

if [ $? -ne 0 ]; then
    echo "CMake配置失败！"
    echo "尝试直接编译..."
    cd ..
    echo "使用g++直接编译（模拟模式）..."
    g++ -std=c++17 -O2 opcua.cpp -o opcua_client
    if [ $? -ne 0 ]; then
        echo "编译失败！请检查是否安装了C++编译器。"
        echo "Ubuntu/Debian: sudo apt-get install build-essential"
        echo "CentOS/RHEL: sudo yum install gcc-c++"
        echo "macOS: xcode-select --install"
        exit 1
    fi
    echo "编译成功！生成了 opcua_client"
    exit 0
fi

echo "编译项目..."
cmake --build . --config Release

if [ $? -ne 0 ]; then
    echo "编译失败！"
    exit 1
fi

echo "编译成功！"
echo "可执行文件位置: build/bin/opcua_client"

echo ""
echo "使用说明："
echo "1. 修改 opcua.cpp 中的 OPCUAClientConfig 类来配置您的服务器"
echo "2. 运行生成的可执行文件: ./build/bin/opcua_client"
echo "3. 查看 README.md 获取详细使用说明"
echo ""

# 使脚本可执行
chmod +x build.sh
