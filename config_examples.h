#pragma once
#include "opcua.cpp"

// 配置示例文件 - 包含各种常见的OPC UA服务器配置

namespace ConfigExamples {

    // 本地测试服务器配置
    OPCUAClientConfig getLocalTestConfig() {
        OPCUAClientConfig config;
        
        // 基本连接设置
        config.serverUrl = "opc.tcp://localhost:4840";
        config.serverName = "本地测试服务器";
        config.serverHost = "localhost";
        config.serverPort = 4840;
        
        // 无安全设置（测试用）
        config.securityPolicy = "None";
        config.securityMode = "None";
        config.useAuthentication = false;
        config.authenticationMethod = "Anonymous";
        
        // 基本节点配置
        config.nodeIds = {
            "ns=0;i=2258",  // Server.ServerStatus.CurrentTime
            "ns=0;i=2259",  // Server.ServerStatus.State
            "ns=2;i=2",     // 自定义节点示例
            "ns=2;s=Demo.Static.Scalar.Boolean",
            "ns=2;s=Demo.Static.Scalar.Int32"
        };
        
        // 快速响应设置
        config.connectionTimeout = 3000;
        config.sessionTimeout = 30000;
        config.publishingInterval = 1000.0;
        config.samplingInterval = 500.0;
        
        return config;
    }

    // 工业PLC配置（西门子S7-1500示例）
    OPCUAClientConfig getSiemensS7Config() {
        OPCUAClientConfig config;
        
        // PLC连接设置
        config.serverUrl = "opc.tcp://************:4840";
        config.serverName = "Siemens S7-1500 PLC";
        config.serverHost = "************";
        config.serverPort = 4840;
        
        // 安全设置
        config.securityPolicy = "Basic256Sha256";
        config.securityMode = "SignAndEncrypt";
        config.useAuthentication = true;
        config.username = "opcua_user";
        config.password = "secure_password";
        config.authenticationMethod = "UserName";
        
        // 工业节点配置
        config.nodeIds = {
            "ns=3;s=\"DB1\".\"Temperature\"",
            "ns=3;s=\"DB1\".\"Pressure\"", 
            "ns=3;s=\"DB1\".\"FlowRate\"",
            "ns=3;s=\"DB1\".\"MotorSpeed\"",
            "ns=3;s=\"DB1\".\"AlarmStatus\"",
            "ns=3;s=\"DB2\".\"ProductionCount\"",
            "ns=3;s=\"DB2\".\"QualityStatus\""
        };
        
        // 工业级超时设置
        config.connectionTimeout = 10000;
        config.sessionTimeout = 120000;
        config.requestTimeout = 15000;
        config.maxRetryCount = 5;
        config.retryDelay = 2000;
        
        // 高频数据采集
        config.publishingInterval = 250.0;
        config.samplingInterval = 100.0;
        config.queueSize = 50;
        
        return config;
    }

    // SCADA系统配置
    OPCUAClientConfig getSCADAConfig() {
        OPCUAClientConfig config;
        
        // SCADA服务器设置
        config.serverUrl = "opc.tcp://scada-server:4840";
        config.serverName = "SCADA监控系统";
        config.serverHost = "scada-server";
        config.serverPort = 4840;
        
        // 证书认证
        config.securityPolicy = "Basic256Sha256";
        config.securityMode = "SignAndEncrypt";
        config.useAuthentication = true;
        config.authenticationMethod = "Certificate";
        config.certificatePath = "./certificates/scada_client.der";
        config.privateKeyPath = "./certificates/scada_client_key.pem";
        config.trustListPath = "./certificates/trusted/";
        
        // SCADA监控节点
        config.nodeIds = {
            "ns=4;s=Plant.Area1.Tank1.Level",
            "ns=4;s=Plant.Area1.Tank1.Temperature",
            "ns=4;s=Plant.Area1.Pump1.Status",
            "ns=4;s=Plant.Area1.Pump1.FlowRate",
            "ns=4;s=Plant.Area2.Reactor.Temperature",
            "ns=4;s=Plant.Area2.Reactor.Pressure",
            "ns=4;s=Plant.Safety.EmergencyStop",
            "ns=4;s=Plant.Safety.AlarmCount"
        };
        
        // SCADA级别的可靠性设置
        config.connectionTimeout = 15000;
        config.sessionTimeout = 300000;  // 5分钟
        config.autoReconnect = true;
        config.reconnectInterval = 5000;
        config.maxRetryCount = 10;
        
        // 中等频率监控
        config.publishingInterval = 2000.0;
        config.samplingInterval = 1000.0;
        config.queueSize = 100;
        
        return config;
    }

    // 云端IoT配置
    OPCUAClientConfig getCloudIoTConfig() {
        OPCUAClientConfig config;
        
        // 云端服务器设置
        config.serverUrl = "opc.tcp://iot-gateway.cloud.com:4840";
        config.serverName = "云端IoT网关";
        config.serverHost = "iot-gateway.cloud.com";
        config.serverPort = 4840;
        
        // 云端安全设置
        config.securityPolicy = "Basic256Sha256";
        config.securityMode = "SignAndEncrypt";
        config.useAuthentication = true;
        config.username = "iot_device_001";
        config.password = "complex_cloud_password_123";
        config.authenticationMethod = "UserName";
        config.validateServerCertificate = true;
        
        // IoT传感器节点
        config.nodeIds = {
            "ns=5;s=Device001.Temperature",
            "ns=5;s=Device001.Humidity",
            "ns=5;s=Device001.BatteryLevel",
            "ns=5;s=Device002.Vibration",
            "ns=5;s=Device002.Noise",
            "ns=5;s=Gateway.Status",
            "ns=5;s=Gateway.ConnectedDevices"
        };
        
        // 云端网络考虑的超时设置
        config.connectionTimeout = 30000;  // 30秒
        config.sessionTimeout = 600000;    // 10分钟
        config.requestTimeout = 20000;     // 20秒
        config.maxRetryCount = 3;
        config.retryDelay = 5000;
        
        // 低频率数据传输（节省带宽）
        config.publishingInterval = 10000.0;  // 10秒
        config.samplingInterval = 5000.0;     // 5秒
        config.queueSize = 20;
        
        // 性能优化
        config.maxMessageSize = 32768;
        config.sendBufferSize = 32768;
        config.receiveBufferSize = 32768;
        
        return config;
    }

    // 高性能实时系统配置
    OPCUAClientConfig getHighPerformanceConfig() {
        OPCUAClientConfig config;
        
        // 高性能服务器设置
        config.serverUrl = "opc.tcp://realtime-server:4840";
        config.serverName = "实时控制系统";
        config.serverHost = "realtime-server";
        config.serverPort = 4840;
        
        // 最小安全开销
        config.securityPolicy = "None";
        config.securityMode = "None";
        config.useAuthentication = false;
        config.authenticationMethod = "Anonymous";
        
        // 高频控制节点
        config.nodeIds = {
            "ns=6;s=Control.Axis1.Position",
            "ns=6;s=Control.Axis1.Velocity",
            "ns=6;s=Control.Axis1.Torque",
            "ns=6;s=Control.Axis2.Position",
            "ns=6;s=Control.Axis2.Velocity",
            "ns=6;s=Control.Axis2.Torque",
            "ns=6;s=System.CycleTime",
            "ns=6;s=System.Performance"
        };
        
        // 极快响应设置
        config.connectionTimeout = 1000;
        config.sessionTimeout = 10000;
        config.requestTimeout = 500;
        config.maxRetryCount = 1;
        config.retryDelay = 100;
        
        // 高频数据采集
        config.publishingInterval = 10.0;   // 10ms
        config.samplingInterval = 5.0;      // 5ms
        config.queueSize = 1000;
        config.maxNotificationsPerPublish = 100;
        
        // 高性能缓冲区
        config.maxMessageSize = 262144;     // 256KB
        config.sendBufferSize = 262144;
        config.receiveBufferSize = 262144;
        
        return config;
    }

    // 调试和开发配置
    OPCUAClientConfig getDebugConfig() {
        OPCUAClientConfig config;
        
        // 开发服务器设置
        config.serverUrl = "opc.tcp://dev-server:4840";
        config.serverName = "开发测试服务器";
        
        // 无安全（开发环境）
        config.securityPolicy = "None";
        config.securityMode = "None";
        config.useAuthentication = false;
        
        // 测试节点
        config.nodeIds = {
            "ns=2;s=TestNode1",
            "ns=2;s=TestNode2",
            "ns=2;s=DebugCounter",
            "ns=2;s=RandomValue"
        };
        
        // 调试友好的超时设置
        config.connectionTimeout = 60000;   // 1分钟（方便调试）
        config.sessionTimeout = 300000;    // 5分钟
        config.requestTimeout = 30000;     // 30秒
        
        // 详细日志
        config.enableLogging = true;
        config.logLevel = "DEBUG";
        config.logFilePath = "debug_opcua.log";
        
        // 慢速采集（便于观察）
        config.publishingInterval = 5000.0;
        config.samplingInterval = 2000.0;
        
        return config;
    }

} // namespace ConfigExamples
