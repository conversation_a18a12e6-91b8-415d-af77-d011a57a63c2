cmake_minimum_required(VERSION 3.10)
project(OPCUAClient)

# 设置C++标准
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 添加编译选项
if(MSVC)
    add_compile_options(/W4)
else()
    add_compile_options(-Wall -Wextra -pedantic)
endif()

# 查找open62541库（可选）
find_package(open62541 QUIET)

# 创建可执行文件
add_executable(opcua_client opcua.cpp)

# 如果找到了open62541库，则启用实际的OPC UA功能
if(open62541_FOUND)
    message(STATUS "Found open62541 library, enabling OPC UA functionality")
    target_link_libraries(opcua_client open62541::open62541)
    target_compile_definitions(opcua_client PRIVATE USE_OPEN62541)
else()
    message(STATUS "open62541 library not found, using simulation mode")
    message(STATUS "To install open62541:")
    message(STATUS "  - Ubuntu/Debian: sudo apt-get install libopen62541-dev")
    message(STATUS "  - Windows: vcpkg install open62541")
    message(STATUS "  - Or build from source: https://github.com/open62541/open62541")
endif()

# 设置输出目录
set_target_properties(opcua_client PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin
)

# 安装规则
install(TARGETS opcua_client
    RUNTIME DESTINATION bin
)

# 打印构建信息
message(STATUS "Build type: ${CMAKE_BUILD_TYPE}")
message(STATUS "C++ compiler: ${CMAKE_CXX_COMPILER}")
message(STATUS "C++ standard: ${CMAKE_CXX_STANDARD}")
