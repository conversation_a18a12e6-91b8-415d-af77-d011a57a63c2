@echo off
chcp 65001 >nul
echo OPC UA C++ Client Build Script
echo ===============================

REM Set MinGW path
set MINGW_PATH=D:\Development\MinGW\mingw64\bin
echo Setting MinGW path: %MINGW_PATH%

REM Add MinGW to PATH
set PATH=%MINGW_PATH%;%PATH%

REM Check if g++ is available
g++ --version >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo Error: g++ compiler not found!
    echo Please check MinGW path: %MINGW_PATH%
    pause
    exit /b 1
)

echo Found g++ compiler, version info:
g++ --version

REM Check if build directory exists
if not exist build (
    echo Creating build directory...
    mkdir build
)

cd build

echo Configuring CMake project...
cmake ..

if %ERRORLEVEL% neq 0 (
    echo CMake configuration failed!
    echo Trying direct compilation...
    cd ..
    echo Using g++ direct compilation (simulation mode)...
    echo Compile command: g++ -std=c++17 -O2 opcua.cpp -o opcua_client.exe
    g++ -std=c++17 -O2 opcua.cpp -o opcua_client.exe
    if %ERRORLEVEL% neq 0 (
        echo Compilation failed!
        echo Please check:
        echo 1. MinGW path is correct: %MINGW_PATH%
        echo 2. g++ compiler is working properly
        echo 3. opcua.cpp file exists
        pause
        exit /b 1
    )
    echo Compilation successful! Generated opcua_client.exe
    goto :end
)

echo Building project...
cmake --build . --config Release

if %ERRORLEVEL% neq 0 (
    echo Build failed!
    pause
    exit /b 1
)

echo Build successful!
echo Executable location: build\bin\Release\opcua_client.exe or build\bin\opcua_client.exe

:end
echo.
echo Usage instructions:
echo 1. Modify OPCUAClientConfig class in opcua.cpp to configure your server
echo 2. Run the generated executable
echo 3. See README.md for detailed usage instructions
echo.
pause
