@echo off
echo OPC UA C++ 客户端编译脚本
echo ========================

REM 检查是否存在build目录
if not exist build (
    echo 创建build目录...
    mkdir build
)

cd build

echo 配置CMake项目...
cmake ..

if %ERRORLEVEL% neq 0 (
    echo CMake配置失败！
    echo 尝试直接编译...
    cd ..
    echo 使用g++直接编译（模拟模式）...
    g++ -std=c++17 -O2 opcua.cpp -o opcua_client.exe
    if %ERRORLEVEL% neq 0 (
        echo 编译失败！请检查是否安装了C++编译器。
        pause
        exit /b 1
    )
    echo 编译成功！生成了 opcua_client.exe
    goto :end
)

echo 编译项目...
cmake --build . --config Release

if %ERRORLEVEL% neq 0 (
    echo 编译失败！
    pause
    exit /b 1
)

echo 编译成功！
echo 可执行文件位置: build\bin\Release\opcua_client.exe 或 build\bin\opcua_client.exe

:end
echo.
echo 使用说明：
echo 1. 修改 opcua.cpp 中的 OPCUAClientConfig 类来配置您的服务器
echo 2. 运行生成的可执行文件
echo 3. 查看 README.md 获取详细使用说明
echo.
pause
