@echo off
chcp 65001 >nul
echo OPC UA C++ Client Build Script
echo ===============================

REM Set MinGW path
set MINGW_PATH=D:\Development\MinGW\mingw64\bin
echo Setting MinGW path: %MINGW_PATH%

REM Add MinGW to PATH
set PATH=%MINGW_PATH%;%PATH%

REM Check if g++ is available
g++ --version >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo Error: g++ compiler not found!
    echo Please check MinGW path: %MINGW_PATH%
    pause
    exit /b 1
)

echo Found g++ compiler, version info:
g++ --version

REM Check if opcua.cpp exists
if not exist opcua.cpp (
    echo Error: opcua.cpp file not found!
    pause
    exit /b 1
)

echo Using g++ direct compilation...
echo Compile command: g++ -std=c++17 -O2 -Wall opcua.cpp -o opcua_client.exe

g++ -std=c++17 -O2 -Wall opcua.cpp -o opcua_client.exe

if %ERRORLEVEL% neq 0 (
    echo Compilation failed!
    echo Please check:
    echo 1. MinGW path is correct: %MINGW_PATH%
    echo 2. g++ compiler is working properly
    echo 3. opcua.cpp file syntax is correct
    pause
    exit /b 1
)

echo Compilation successful! Generated opcua_client.exe
echo Executable location: opcua_client.exe

:end
echo.
echo Usage instructions:
echo 1. Modify OPCUAClientConfig class in opcua.cpp to configure your server
echo 2. Run the generated executable
echo 3. See README.md for detailed usage instructions
echo.
pause
