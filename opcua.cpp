#include <iostream>
#include <string>
#include <vector>
#include <chrono>
#include <thread>

// 假设使用open62541库，这是一个流行的开源OPC UA库
// 如果您使用其他库，请相应调整头文件
#ifdef USE_OPEN62541
#include <open62541/client.h>
#include <open62541/client_config_default.h>
#include <open62541/client_highlevel.h>
#include <open62541/plugin/log_stdout.h>
#endif

class OPCUAClientConfig {
public:
    // 服务器连接配置
    std::string serverUrl = "opc.tcp://localhost:4840";  // 服务器URL
    std::string serverName = "OPC UA Server";            // 服务器名称
    int serverPort = 4840;                               // 服务器端口
    std::string serverHost = "localhost";                // 服务器主机

    // 安全配置
    std::string securityPolicy = "None";                 // 安全策略: None, Basic128Rsa15, Basic256, Basic256Sha256
    std::string securityMode = "None";                   // 安全模式: None, Sign, SignAndEncrypt
    std::string certificatePath = "";                    // 客户端证书路径
    std::string privateKeyPath = "";                     // 客户端私钥路径
    std::string trustListPath = "";                      // 信任列表路径

    // 认证配置
    bool useAuthentication = false;                      // 是否使用认证
    std::string username = "";                           // 用户名
    std::string password = "";                           // 密码
    std::string authenticationMethod = "Anonymous";      // 认证方法: Anonymous, UserName, Certificate

    // 连接配置
    int connectionTimeout = 5000;                        // 连接超时时间(毫秒)
    int sessionTimeout = 60000;                          // 会话超时时间(毫秒)
    int requestTimeout = 10000;                          // 请求超时时间(毫秒)
    int maxRetryCount = 3;                               // 最大重试次数
    int retryDelay = 1000;                               // 重试延迟(毫秒)

    // 订阅配置
    double publishingInterval = 1000.0;                  // 发布间隔(毫秒)
    int maxNotificationsPerPublish = 10;                 // 每次发布的最大通知数
    int lifetimeCount = 10000;                           // 生命周期计数
    int maxKeepAliveCount = 3;                           // 最大保活计数
    int priority = 0;                                    // 优先级

    // 监控项配置
    double samplingInterval = 1000.0;                    // 采样间隔(毫秒)
    int queueSize = 10;                                  // 队列大小
    bool discardOldest = true;                           // 是否丢弃最旧的数据

    // 节点配置
    std::vector<std::string> nodeIds = {                 // 要监控的节点ID列表
        "ns=2;i=2",
        "ns=2;i=3",
        "ns=2;s=Temperature",
        "ns=2;s=Pressure"
    };

    // 应用程序配置
    std::string applicationName = "OPC UA C++ Client";   // 应用程序名称
    std::string applicationUri = "urn:opcua:client";     // 应用程序URI
    std::string productUri = "http://example.com/client"; // 产品URI

    // 日志配置
    bool enableLogging = true;                           // 是否启用日志
    std::string logLevel = "INFO";                       // 日志级别: TRACE, DEBUG, INFO, WARNING, ERROR, FATAL
    std::string logFilePath = "opcua_client.log";       // 日志文件路径

    // 性能配置
    int maxMessageSize = 65536;                          // 最大消息大小
    int maxChunkCount = 0;                               // 最大块数量(0表示无限制)
    int sendBufferSize = 65536;                          // 发送缓冲区大小
    int receiveBufferSize = 65536;                       // 接收缓冲区大小

    // 其他配置
    bool autoReconnect = true;                           // 是否自动重连
    int reconnectInterval = 5000;                        // 重连间隔(毫秒)
    bool validateServerCertificate = false;              // 是否验证服务器证书
    std::string locale = "en-US";                        // 语言环境
};

class OPCUAClient {
private:
    OPCUAClientConfig config;
    bool connected = false;

#ifdef USE_OPEN62541
    UA_Client* client = nullptr;
    UA_ClientConfig* clientConfig = nullptr;
#endif

public:
    OPCUAClient(const OPCUAClientConfig& cfg) : config(cfg) {
        initializeClient();
    }

    ~OPCUAClient() {
        disconnect();
        cleanup();
    }

    void initializeClient() {
#ifdef USE_OPEN62541
        client = UA_Client_new();
        clientConfig = UA_Client_getConfig(client);

        // 设置默认配置
        UA_ClientConfig_setDefault(clientConfig);

        // 应用配置参数
        clientConfig->timeout = config.connectionTimeout;
        clientConfig->sessionTimeout = config.sessionTimeout;

        // 设置应用程序描述
        clientConfig->clientDescription.applicationName =
            UA_LOCALIZEDTEXT((char*)"en-US", (char*)config.applicationName.c_str());
        clientConfig->clientDescription.applicationUri =
            UA_STRING((char*)config.applicationUri.c_str());
        clientConfig->clientDescription.productUri =
            UA_STRING((char*)config.productUri.c_str());

        if (config.enableLogging) {
            // 设置日志级别
            UA_LogLevel logLevel = UA_LOGLEVEL_INFO;
            if (config.logLevel == "TRACE") logLevel = UA_LOGLEVEL_TRACE;
            else if (config.logLevel == "DEBUG") logLevel = UA_LOGLEVEL_DEBUG;
            else if (config.logLevel == "WARNING") logLevel = UA_LOGLEVEL_WARNING;
            else if (config.logLevel == "ERROR") logLevel = UA_LOGLEVEL_ERROR;
            else if (config.logLevel == "FATAL") logLevel = UA_LOGLEVEL_FATAL;

            clientConfig->logger = UA_Log_Stdout_withLevel(logLevel);
        }
#endif

        std::cout << "OPC UA客户端初始化完成" << std::endl;
        printConfiguration();
    }

    bool connect() {
        std::cout << "正在连接到服务器: " << config.serverUrl << std::endl;

#ifdef USE_OPEN62541
        UA_StatusCode retval;

        if (config.useAuthentication && config.authenticationMethod == "UserName") {
            // 使用用户名密码认证
            retval = UA_Client_connectUsername(client, config.serverUrl.c_str(),
                                             config.username.c_str(), config.password.c_str());
        } else {
            // 匿名连接
            retval = UA_Client_connect(client, config.serverUrl.c_str());
        }

        if (retval == UA_STATUSCODE_GOOD) {
            connected = true;
            std::cout << "成功连接到服务器!" << std::endl;
            return true;
        } else {
            std::cout << "连接失败，状态码: " << UA_StatusCode_name(retval) << std::endl;
            return false;
        }
#else
        // 模拟连接（当没有实际的OPC UA库时）
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
        connected = true;
        std::cout << "模拟连接成功!" << std::endl;
        return true;
#endif
    }

    void disconnect() {
        if (connected) {
            std::cout << "正在断开连接..." << std::endl;

#ifdef USE_OPEN62541
            UA_Client_disconnect(client);
#endif
            connected = false;
            std::cout << "已断开连接" << std::endl;
        }
    }

    bool readNode(const std::string& nodeId) {
        if (!connected) {
            std::cout << "错误: 客户端未连接" << std::endl;
            return false;
        }

        std::cout << "读取节点: " << nodeId << std::endl;

#ifdef USE_OPEN62541
        UA_NodeId node = UA_NODEID_STRING(1, (char*)nodeId.c_str());
        UA_Variant value;
        UA_Variant_init(&value);

        UA_StatusCode retval = UA_Client_readValueAttribute(client, node, &value);

        if (retval == UA_STATUSCODE_GOOD) {
            std::cout << "读取成功，数据类型: " << value.type->typeName << std::endl;
            // 这里可以根据数据类型进行具体的值解析
            UA_Variant_clear(&value);
            return true;
        } else {
            std::cout << "读取失败，状态码: " << UA_StatusCode_name(retval) << std::endl;
            return false;
        }
#else
        // 模拟读取
        std::cout << "模拟读取节点值: 42.5" << std::endl;
        return true;
#endif
    }

    void readAllConfiguredNodes() {
        std::cout << "读取所有配置的节点..." << std::endl;
        for (const auto& nodeId : config.nodeIds) {
            readNode(nodeId);
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
        }
    }

    void printConfiguration() {
        std::cout << "\n=== OPC UA客户端配置 ===" << std::endl;
        std::cout << "服务器URL: " << config.serverUrl << std::endl;
        std::cout << "服务器名称: " << config.serverName << std::endl;
        std::cout << "安全策略: " << config.securityPolicy << std::endl;
        std::cout << "安全模式: " << config.securityMode << std::endl;
        std::cout << "认证方法: " << config.authenticationMethod << std::endl;
        std::cout << "连接超时: " << config.connectionTimeout << "ms" << std::endl;
        std::cout << "会话超时: " << config.sessionTimeout << "ms" << std::endl;
        std::cout << "应用程序名称: " << config.applicationName << std::endl;
        std::cout << "监控节点数量: " << config.nodeIds.size() << std::endl;
        std::cout << "自动重连: " << (config.autoReconnect ? "是" : "否") << std::endl;
        std::cout << "========================\n" << std::endl;
    }

    void cleanup() {
#ifdef USE_OPEN62541
        if (client) {
            UA_Client_delete(client);
            client = nullptr;
        }
#endif
    }

    bool isConnected() const {
        return connected;
    }

    // 获取和设置配置的方法
    OPCUAClientConfig& getConfig() {
        return config;
    }

    void setConfig(const OPCUAClientConfig& newConfig) {
        config = newConfig;
    }
};

// 示例使用函数
void demonstrateBasicUsage() {
    std::cout << "=== 基本使用示例 ===" << std::endl;

    // 创建默认配置
    OPCUAClientConfig config;

    // 您可以在这里修改配置参数
    // config.serverUrl = "opc.tcp://192.168.1.100:4840";
    // config.username = "admin";
    // config.password = "password";
    // config.useAuthentication = true;
    // config.authenticationMethod = "UserName";

    // 创建客户端
    OPCUAClient client(config);

    // 连接到服务器
    if (client.connect()) {
        // 读取单个节点
        client.readNode("ns=2;i=2");

        // 读取所有配置的节点
        client.readAllConfiguredNodes();

        // 保持连接一段时间
        std::this_thread::sleep_for(std::chrono::seconds(2));

        // 断开连接
        client.disconnect();
    } else {
        std::cout << "无法连接到服务器" << std::endl;
    }
}

void demonstrateAdvancedConfiguration() {
    std::cout << "\n=== 高级配置示例 ===" << std::endl;

    // 创建自定义配置
    OPCUAClientConfig config;

    // 服务器配置
    config.serverUrl = "opc.tcp://industrial-server:4840";
    config.serverName = "工业控制服务器";

    // 安全配置
    config.securityPolicy = "Basic256Sha256";
    config.securityMode = "SignAndEncrypt";
    config.certificatePath = "./certificates/client_cert.der";
    config.privateKeyPath = "./certificates/client_key.pem";
    config.trustListPath = "./certificates/trusted/";

    // 认证配置
    config.useAuthentication = true;
    config.username = "operator";
    config.password = "secure_password";
    config.authenticationMethod = "UserName";

    // 连接配置
    config.connectionTimeout = 10000;
    config.sessionTimeout = 120000;
    config.maxRetryCount = 5;
    config.retryDelay = 2000;

    // 监控节点配置
    config.nodeIds = {
        "ns=3;s=Line1.Temperature",
        "ns=3;s=Line1.Pressure",
        "ns=3;s=Line1.FlowRate",
        "ns=3;s=Line2.Temperature",
        "ns=3;s=Line2.Pressure",
        "ns=3;s=System.Status",
        "ns=3;s=System.AlarmCount"
    };

    // 订阅配置
    config.publishingInterval = 500.0;  // 更快的更新频率
    config.samplingInterval = 250.0;   // 更快的采样
    config.queueSize = 20;              // 更大的队列

    // 日志配置
    config.enableLogging = true;
    config.logLevel = "DEBUG";
    config.logFilePath = "industrial_client.log";

    // 性能配置
    config.maxMessageSize = 131072;     // 更大的消息大小
    config.sendBufferSize = 131072;
    config.receiveBufferSize = 131072;

    // 其他配置
    config.autoReconnect = true;
    config.reconnectInterval = 3000;
    config.validateServerCertificate = true;
    config.locale = "zh-CN";

    // 创建客户端
    OPCUAClient client(config);

    std::cout << "高级配置客户端已创建，配置如下：" << std::endl;
    client.printConfiguration();
}

void demonstrateConfigurationModification() {
    std::cout << "\n=== 运行时配置修改示例 ===" << std::endl;

    // 创建客户端
    OPCUAClientConfig config;
    OPCUAClient client(config);

    // 获取当前配置
    OPCUAClientConfig& currentConfig = client.getConfig();

    std::cout << "修改前的服务器URL: " << currentConfig.serverUrl << std::endl;

    // 修改配置
    currentConfig.serverUrl = "opc.tcp://new-server:4840";
    currentConfig.connectionTimeout = 15000;
    currentConfig.nodeIds.push_back("ns=4;s=NewNode");

    std::cout << "修改后的服务器URL: " << currentConfig.serverUrl << std::endl;
    std::cout << "新的连接超时: " << currentConfig.connectionTimeout << "ms" << std::endl;
    std::cout << "节点数量: " << currentConfig.nodeIds.size() << std::endl;

    // 或者创建全新的配置
    OPCUAClientConfig newConfig;
    newConfig.serverUrl = "opc.tcp://backup-server:4840";
    newConfig.applicationName = "备份客户端";

    client.setConfig(newConfig);
    std::cout << "已设置新配置，新的应用程序名称: " << client.getConfig().applicationName << std::endl;
}

int main() {
    std::cout << "OPC UA C++ 客户端示例程序" << std::endl;
    std::cout << "================================" << std::endl;

    try {
        // 基本使用示例
        demonstrateBasicUsage();

        // 高级配置示例
        demonstrateAdvancedConfiguration();

        // 配置修改示例
        demonstrateConfigurationModification();

    } catch (const std::exception& e) {
        std::cerr << "发生异常: " << e.what() << std::endl;
        return 1;
    }

    std::cout << "\n程序执行完成。" << std::endl;
    std::cout << "\n使用说明：" << std::endl;
    std::cout << "1. 修改 OPCUAClientConfig 类中的默认值来配置您的服务器" << std::endl;
    std::cout << "2. 如果您有 open62541 库，请定义 USE_OPEN62541 宏来启用实际的OPC UA功能" << std::endl;
    std::cout << "3. 根据您的服务器调整节点ID、安全设置和认证参数" << std::endl;
    std::cout << "4. 编译时链接相应的OPC UA库" << std::endl;

    return 0;
}