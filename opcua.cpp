#include <iostream>
#include <string>
#include <vector>
#include <chrono>
#include <thread>
#include <locale>
// 假设使用open62541库，这是一个流行的开源OPC UA库
// 如果您使用其他库，请相应调整头文件
#ifdef USE_OPEN62541
#include <winsock2.h>  // 必须在windows.h之前包含
#include <open62541/client.h>
#include <open62541/client_config_default.h>
#include <open62541/client_highlevel.h>
#include <open62541/plugin/log_stdout.h>
#endif

#ifdef _WIN32
#include <windows.h>
#include <io.h>
#include <fcntl.h>
#endif

class OPCUAClientConfig {
public:
    // 服务器连接配置
    std::string serverUrl = "opc.tcp://localhost:4840";  // 服务器URL
    std::string serverName = "OPC UA Server";            // 服务器名称
    int serverPort = 4840;                               // 服务器端口
    std::string serverHost = "localhost";                // 服务器主机

    // 安全配置
    std::string securityPolicy = "None";                 // 安全策略: None, Basic128Rsa15, Basic256, Basic256Sha256
    std::string securityMode = "None";                   // 安全模式: None, Sign, SignAndEncrypt
    std::string certificatePath = "";                    // 客户端证书路径
    std::string privateKeyPath = "";                     // 客户端私钥路径
    std::string trustListPath = "";                      // 信任列表路径

    // 认证配置
    bool useAuthentication = false;                      // 是否使用认证
    std::string username = "";                           // 用户名
    std::string password = "";                           // 密码
    std::string authenticationMethod = "Anonymous";      // 认证方法: Anonymous, UserName, Certificate

    // 连接配置
    int connectionTimeout = 5000;                        // 连接超时时间(毫秒)
    int sessionTimeout = 60000;                          // 会话超时时间(毫秒)
    int requestTimeout = 10000;                          // 请求超时时间(毫秒)
    int maxRetryCount = 3;                               // 最大重试次数
    int retryDelay = 1000;                               // 重试延迟(毫秒)

    // 订阅配置
    double publishingInterval = 1000.0;                  // 发布间隔(毫秒)
    int maxNotificationsPerPublish = 10;                 // 每次发布的最大通知数
    int lifetimeCount = 10000;                           // 生命周期计数
    int maxKeepAliveCount = 3;                           // 最大保活计数
    int priority = 0;                                    // 优先级

    // 监控项配置
    double samplingInterval = 1000.0;                    // 采样间隔(毫秒)
    int queueSize = 10;                                  // 队列大小
    bool discardOldest = true;                           // 是否丢弃最旧的数据

    // 节点配置
    std::vector<std::string> nodeIds = {                 // 要监控的节点ID列表
        "ns=2;i=2",
        "ns=2;i=3",
        "ns=2;s=Temperature",
        "ns=2;s=Pressure"
    };

    // 应用程序配置
    std::string applicationName = "OPC UA C++ Client";   // 应用程序名称
    std::string applicationUri = "urn:opcua:client";     // 应用程序URI
    std::string productUri = "http://example.com/client"; // 产品URI

    // 日志配置
    bool enableLogging = true;                           // 是否启用日志
    std::string logLevel = "INFO";                       // 日志级别: TRACE, DEBUG, INFO, WARNING, ERROR, FATAL
    std::string logFilePath = "opcua_client.log";       // 日志文件路径

    // 性能配置
    int maxMessageSize = 65536;                          // 最大消息大小
    int maxChunkCount = 0;                               // 最大块数量(0表示无限制)
    int sendBufferSize = 65536;                          // 发送缓冲区大小
    int receiveBufferSize = 65536;                       // 接收缓冲区大小

    // 其他配置
    bool autoReconnect = true;                           // 是否自动重连
    int reconnectInterval = 5000;                        // 重连间隔(毫秒)
    bool validateServerCertificate = false;              // 是否验证服务器证书
    std::string locale = "en-US";                        // 语言环境
};

class OPCUAClient {
private:
    OPCUAClientConfig config;
    bool connected = false;

#ifdef USE_OPEN62541
    UA_Client* client = nullptr;
    UA_ClientConfig* clientConfig = nullptr;
#endif

public:
    OPCUAClient(const OPCUAClientConfig& cfg) : config(cfg) {
        initializeClient();
    }

    ~OPCUAClient() {
        disconnect();
        cleanup();
    }

    void initializeClient() {
#ifdef USE_OPEN62541
        client = UA_Client_new();
        clientConfig = UA_Client_getConfig(client);

        // 设置默认配置
        UA_ClientConfig_setDefault(clientConfig);

        // 应用配置参数
        clientConfig->timeout = config.connectionTimeout;
        // 注意：新版本的open62541可能没有sessionTimeout字段
        // clientConfig->sessionTimeout = config.sessionTimeout;

        // 设置应用程序描述
        clientConfig->clientDescription.applicationName =
            UA_LOCALIZEDTEXT((char*)"en-US", (char*)config.applicationName.c_str());
        clientConfig->clientDescription.applicationUri =
            UA_STRING((char*)config.applicationUri.c_str());
        clientConfig->clientDescription.productUri =
            UA_STRING((char*)config.productUri.c_str());

        // 禁用日志输出以避免乱码问题
        if (config.enableLogging) {
            // 设置日志级别
            UA_LogLevel logLevel = UA_LOGLEVEL_ERROR;  // 只显示错误信息
            if (config.logLevel == "TRACE") logLevel = UA_LOGLEVEL_TRACE;
            else if (config.logLevel == "DEBUG") logLevel = UA_LOGLEVEL_DEBUG;
            else if (config.logLevel == "WARNING") logLevel = UA_LOGLEVEL_WARNING;
            else if (config.logLevel == "ERROR") logLevel = UA_LOGLEVEL_ERROR;
            else if (config.logLevel == "FATAL") logLevel = UA_LOGLEVEL_FATAL;

            clientConfig->logger = UA_Log_Stdout_withLevel(logLevel);
        } else {
            // 设置最高日志级别来减少输出
            clientConfig->logger = UA_Log_Stdout_withLevel(UA_LOGLEVEL_FATAL);
        }
#endif

        std::cout << "OPC UA客户端初始化完成" << std::endl;
        printConfiguration();
    }

    bool connect() {
        std::cout << "正在连接到服务器: " << config.serverUrl << std::endl;

#ifdef USE_OPEN62541
        UA_StatusCode retval;

        if (config.useAuthentication && config.authenticationMethod == "UserName") {
            // 使用用户名密码认证
            retval = UA_Client_connectUsername(client, config.serverUrl.c_str(),
                                             config.username.c_str(), config.password.c_str());
        } else {
            // 匿名连接
            retval = UA_Client_connect(client, config.serverUrl.c_str());
        }

        if (retval == UA_STATUSCODE_GOOD) {
            connected = true;
            std::cout << "成功连接到服务器!" << std::endl;
            return true;
        } else {
            std::cout << "连接失败，状态码: " << UA_StatusCode_name(retval) << std::endl;
            return false;
        }
#else
        std::cout << "错误: 未编译OPC UA库支持!" << std::endl;
        std::cout << "请安装open62541库并使用 -DUSE_OPEN62541 编译选项" << std::endl;
        return false;
#endif
    }

    void disconnect() {
        if (connected) {
            std::cout << "正在断开连接..." << std::endl;

#ifdef USE_OPEN62541
            UA_Client_disconnect(client);
#endif
            connected = false;
            std::cout << "已断开连接" << std::endl;
        }
    }

    bool readNode(const std::string& nodeId) {
        if (!connected) {
            std::cout << "错误: 客户端未连接" << std::endl;
            return false;
        }

        std::cout << "读取节点: " << nodeId << std::endl;

#ifdef USE_OPEN62541
        UA_NodeId node = UA_NODEID_STRING(1, (char*)nodeId.c_str());
        UA_Variant value;
        UA_Variant_init(&value);

        UA_StatusCode retval = UA_Client_readValueAttribute(client, node, &value);

        if (retval == UA_STATUSCODE_GOOD) {
            std::cout << "读取成功，数据类型: " << value.type->typeName << std::endl;
            // 这里可以根据数据类型进行具体的值解析
            UA_Variant_clear(&value);
            return true;
        } else {
            std::cout << "读取失败，状态码: " << UA_StatusCode_name(retval) << std::endl;
            return false;
        }
#else
        std::cout << "错误: 未编译OPC UA库支持!" << std::endl;
        std::cout << "请安装open62541库并使用 -DUSE_OPEN62541 编译选项" << std::endl;
        return false;
#endif
    }

    void readAllConfiguredNodes() {
        std::cout << "读取所有配置的节点..." << std::endl;
        for (const auto& nodeId : config.nodeIds) {
            readNode(nodeId);
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
        }
    }

    void printConfiguration() {
        std::cout << "\n=== OPC UA客户端配置 ===" << std::endl;
        std::cout << "服务器URL: " << config.serverUrl << std::endl;
        std::cout << "服务器名称: " << config.serverName << std::endl;
        std::cout << "安全策略: " << config.securityPolicy << std::endl;
        std::cout << "安全模式: " << config.securityMode << std::endl;
        std::cout << "认证方法: " << config.authenticationMethod << std::endl;
        std::cout << "连接超时: " << config.connectionTimeout << "ms" << std::endl;
        std::cout << "会话超时: " << config.sessionTimeout << "ms" << std::endl;
        std::cout << "应用程序名称: " << config.applicationName << std::endl;
        std::cout << "监控节点数量: " << config.nodeIds.size() << std::endl;
        std::cout << "自动重连: " << (config.autoReconnect ? "是" : "否") << std::endl;
        std::cout << "========================\n" << std::endl;
    }

    void cleanup() {
#ifdef USE_OPEN62541
        if (client) {
            UA_Client_delete(client);
            client = nullptr;
        }
#endif
    }

    bool isConnected() const {
        return connected;
    }

    // 获取和设置配置的方法
    OPCUAClientConfig& getConfig() {
        return config;
    }

    void setConfig(const OPCUAClientConfig& newConfig) {
        config = newConfig;
    }
};

// 基本使用示例
void basicUsageExample() {
    std::cout << "=== OPC UA 客户端基本使用 ===" << std::endl;

    // 创建默认配置
    OPCUAClientConfig config;

    // 在这里修改您的服务器配置
    // config.serverUrl = "opc.tcp://192.168.1.100:4840";
    // config.username = "admin";
    // config.password = "password";
    // config.useAuthentication = true;
    // config.authenticationMethod = "UserName";

    // 创建客户端
    OPCUAClient client(config);

    // 尝试连接到服务器
    if (client.connect()) {
        std::cout << "连接成功！开始读取数据..." << std::endl;

        // 读取所有配置的节点
        client.readAllConfiguredNodes();

        // 保持连接一段时间
        std::this_thread::sleep_for(std::chrono::seconds(1));

        // 断开连接
        client.disconnect();
    } else {
        std::cout << "连接失败！请检查服务器配置和网络连接。" << std::endl;
    }
}

// 设置控制台编码支持中文
void setupConsoleEncoding() {
#ifdef _WIN32
    // 设置控制台代码页为UTF-8
    SetConsoleOutputCP(CP_UTF8);
    SetConsoleCP(CP_UTF8);

    // 设置控制台模式支持UTF-8
    HANDLE hOut = GetStdHandle(STD_OUTPUT_HANDLE);
    DWORD dwMode = 0;
    GetConsoleMode(hOut, &dwMode);
    dwMode |= ENABLE_PROCESSED_OUTPUT | ENABLE_WRAP_AT_EOL_OUTPUT | ENABLE_VIRTUAL_TERMINAL_PROCESSING;
    SetConsoleMode(hOut, dwMode);

    // 尝试设置中文locale，如果失败则使用默认
    try {
        std::locale::global(std::locale("zh_CN.UTF-8"));
    } catch (...) {
        try {
            std::locale::global(std::locale("Chinese"));
        } catch (...) {
            // 如果都失败，使用默认locale
            std::locale::global(std::locale("C"));
        }
    }
#endif
}

int main() {
    // 设置控制台编码
    setupConsoleEncoding();

    std::cout << "OPC UA C++ 客户端程序" << std::endl;
    std::cout << "=====================" << std::endl;

    try {
        // 运行基本使用示例
        basicUsageExample();

    } catch (const std::exception& e) {
        std::cerr << "发生异常: " << e.what() << std::endl;
        return 1;
    }

    std::cout << "\n程序执行完成。" << std::endl;
    std::cout << "\n配置说明：" << std::endl;
    std::cout << "1. 修改 OPCUAClientConfig 类中的默认值来配置您的服务器" << std::endl;
    std::cout << "2. 安装 open62541 库并使用 -DUSE_OPEN62541 编译选项启用OPC UA功能" << std::endl;
    std::cout << "3. 根据您的服务器调整 serverUrl、节点ID、安全设置和认证参数" << std::endl;

    return 0;
}